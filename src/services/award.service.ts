import dayjs from 'dayjs';
import { Op, Sequelize } from 'sequelize';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import httpStatus from 'http-status';

import { AwardInput } from '../models/types/award';
import ApiError from '../helpers/api-error';
import { getFilterCriteria } from '../helpers/award.helper';
import db from '../models';

import strings from '../config/strings';

import {
  AwardStatus,
  CreateAward,
  FilterAwards,
  UpdateAward,
} from '../controllers/types/award.type';

import { reduceTZ, createXlxsFile, createCsvFile } from '../helpers/exports.helper';
import actionLogService from './action-log.service';

import {
  requestApproval,
  rejectAward,
  approveAward,
  updateAwardStatus,
} from './awards/award-approval.service';

import { getAwardTimelines, syncAwardStatus } from './awards/award-timeline.service';

import {
  recalculateAwardPayments,
  // Standard Methods
  addPayment,
  updatePayment,
  deletePayment,
  getPaymentById,
  listPayments,
} from './awards/award-payment.service';

import {
  getFiles,
  createFile,
  deleteFile,
  downloadZippedFiles,
} from './awards/award-document.service';

import {
  recalculateAwardBudget,
  // Standard Methods
  addBudgetEntry,
  addManyBudgetEntries,
  updateBudgetEntry,
  updateManyBudgetEntries,
  getBudgetEntries,
  deleteBudgetEntry,
} from './awards/award-budget.service';

import { listReports, addReport, updateReport, deleteReport } from './awards/award-report.service';

import {
  getAllUserRoles,
  getUserRoleById,
  createUserRole,
  updateUserRole,
  deleteUserRole,
} from './awards/award-user-role.service';

require('dotenv').config();

const { userAdmin, userAnalyst } = strings.users.userTypes;

const { Award, AwardUserRole, AwardReport, Employee: User, Version, BudgetVersionEntry } = db;
// eslint-disable-next-line no-useless-escape
const rEDate = /[\d\-]{8,10}T[\d\:\.]{8,12}Z/;
dayjs.extend(advancedFormat);

const list = async (query: FilterAwards, currentUser: typeof User) => {
  const { page, perPage, programs, clients, assignees, target } = query;
  const { queryFilters, sortingQuery } = await getFilterCriteria(query);

  const intPage = parseInt(page || '1', 10);
  const intPerPage = parseInt(perPage || '20', 10);

  if (Number.isNaN(intPage) || Number.isNaN(intPerPage))
    throw new ApiError(httpStatus.BAD_REQUEST, `Invalid query parameters`);

  try {
    const awardAttributes: any = {
      where: {
        ...queryFilters,
        ...(currentUser.clientCreatorId && { clientId: currentUser.clientCreatorId }),
      },
      include: [
        {
          model: db.Employee,
          as: 'assignee',
          where: {
            ...(assignees && assignees.trim()
              ? { name: { [Op.in]: assignees.split(',').map((name) => name.trim()) } }
              : {}),
          },
          required: false,
        },
        {
          model: db.Client,
          as: 'client',
          where: clients
            ? { name: { [Op.in]: clients.split(',').map((name) => name.trim()) } }
            : {},
          required: true, // Ensures an inner join
        },
        {
          model: db.Application,
          as: 'application',
        },
        {
          model: db.Program,
          as: 'program',
          where: programs
            ? { name: { [Op.in]: programs.split(',').map((name) => name.trim()) } }
            : {},
          required: false,
        },
        {
          model: AwardReport,
          order: [['endsOn', 'desc']],
          as: 'reports',
        },
      ],
      distinct: true, // Ensure distinct results
      // col: 'awards.id', // Specify the column to apply distinct on
      offset: intPerPage * (intPage - 1),
      limit: intPerPage,
      ...(sortingQuery && { order: sortingQuery }),
      // group: ['awards.id'],
      // subQuery: false, // Add this property to disable subquery in count query
    };

    if (target) {
      awardAttributes.where = {
        ...awardAttributes.where,
        [Op.or]: [{ assigneeId: currentUser.id }],
      };

      awardAttributes.include.push({
        model: db.AwardUserRole,
        as: 'userRoles',
        where: {
          userId: currentUser.id,
          enabled: true,
        },
        required: false,
      });
    }

    // Performance monitoring for sorting queries
    const startTime = Date.now();
    const data = await Award.scope('defaultScope').findAndCountAll(awardAttributes);
    const queryTime = Date.now() - startTime;

    // Log slow queries for performance monitoring
    if (queryTime > 1000) {
      console.warn(`Slow award sorting query: ${queryTime}ms`, {
        sortBy: query.sortBy,
        sortOrder: query.sortOrder,
        page: query.page,
        perPage: query.perPage,
        totalRecords: data.count,
        filters: Object.keys(queryFilters).length > 0 ? Object.keys(queryFilters) : 'none',
      });
    }

    return data;
  } catch (error) {
    console.error(error);

    if (error instanceof ApiError) {
      throw new ApiError(error.statusCode, error.message);
    }

    throw new ApiError(httpStatus.BAD_REQUEST, `Error retrieving awards`);
  }
};

const search = async (
  field: keyof AwardInput,
  query: string,
  currentUser: typeof User,
  clientId?: number
) => {
  try {
    // Sequelize offers NO NATIVE SOLUTION for supporting DISTINCT ON keyword of PostgreSQL. Hacks to the rescue! ¯\_(ツ)_/¯
    // See this old issue: https://github.com/sequelize/sequelize/issues/5475
    const results = await Award.findByQuery(field, query, [
      Sequelize.literal('DISTINCT ON("grant_program_name") "grant_program_name"'),
      'id',
      'clientId',
      'grantProgramName',
      'funder',
      'department',
    ]);

    // const results = await Award.findByQuery(field, query);

    return results.filter((result: typeof Award) => {
      // If user has a client restriction, apply it
      if (currentUser.clientCreatorId) {
        return Number(result.clientId) === Number(currentUser.clientCreatorId);
      }

      // If a specific clientId is provided, filter by it
      if (clientId) {
        return Number(result.clientId) === Number(clientId);
      }

      return true;
    });
  } catch (error) {
    console.error(error);
    throw new ApiError(httpStatus.BAD_REQUEST, `Error searcing awards`);
  }
};

const getById = async (awardId: string, currentUser: typeof User) => {
  try {
    const awardAttributes = {
      where: {
        id: parseInt(awardId, 10),
        ...(currentUser.clientCreatorId && { clientId: currentUser.clientCreatorId }),
      },
      include: [
        {
          model: db.Employee,
          as: 'assignee',
          attributes: {
            exclude: ['password'],
          },
        },
        {
          model: db.Client,
          as: 'client',
        },
        {
          model: db.Application,
          as: 'application',
        },
        {
          model: db.Program,
          as: 'program',
        },
        {
          model: db.AwardNotification,
          as: 'notifications',
        },
      ],
    };

    syncAwardStatus(awardId.toString());

    let award = await Award.scope('defaultScope').findOne(awardAttributes);

    const actionLog = await actionLogService.getByAwardId(Number(awardId));
    const isClientUser = [userAdmin, userAnalyst].includes(currentUser.accessLevel);
    const isDifferentClient = award.clientId !== currentUser.clientCreatorId;

    if (isClientUser && isDifferentClient) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'You cannot view this award.');
    }

    if (!award) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Award not found');
    } else {
      award = await recalculateAwardPayments(award);
      award = await recalculateAwardBudget(award);

      award.dataValues.actionLogs = actionLog?.dataValues || null;

      return award;
    }
  } catch (error) {
    console.error('Get Award', error);

    if (error instanceof ApiError) {
      throw new ApiError(error.statusCode, error.message);
    }

    throw new ApiError(httpStatus.BAD_REQUEST, 'Error retieving award');
  }
};

const createOne = async (awardBody: CreateAward, currentUser: typeof User) => {
  const {
    programId,
    assigneeId,
    clientId,
    applicationId,
    source,
    category,
    funder,
    funderAwardId,
    startsOn,
    endsOn,
    department,
    grantPurpose,
    grantProgramName,
    notes,
    awardAmount,
    awardExpended,
    awardBalance,
    matchAmount,
    matchExpended,
    matchBalance,
    resolutionRequired,
    applicationRequired,
    awardLetterRequired,
    contractMaterialRequired,
    initialAgreementRequired,
    reportRequired,
    awardPortalLink,
  } = awardBody;

  const fields: AwardInput = {
    programId,
    assigneeId,
    clientId,
    applicationId,
    source: Array.isArray(source) ? source[0] : source,
    category: Array.isArray(category) ? category[0] : category,
    funder,
    funderAwardId,
    startsOn: startsOn && dayjs(new Date(startsOn).toISOString()).toDate(),
    endsOn: endsOn && dayjs(new Date(endsOn).toISOString()).toDate(),
    department,
    grantPurpose,
    grantProgramName,
    notes: notes ? `${notes}\n` : undefined,
    awardAmount,
    awardExpended,
    awardBalance,
    matchAmount,
    matchExpended,
    matchBalance,
    status: strings.awards.statusStrings.applicationRequired as AwardStatus,
    resolutionRequired,
    applicationRequired,
    awardLetterRequired,
    contractMaterialRequired,
    initialAgreementRequired,
    reportRequired,
    awardPortalLink,
  };

  // Remove keys from fields wherever the value is undefined.
  Object.keys(fields).forEach(
    (key) => fields[key as keyof AwardInput] === undefined && delete fields[key as keyof AwardInput]
  );
  try {
    const newAward = await Award.create(fields);

    if (assigneeId) {
      AwardUserRole.create({
        awardId: newAward.id,
        userId: assigneeId,
        role: 'projectDirector',
      });
    }

    actionLogService.createAwardActionLog({
      award: newAward.dataValues,
      user: { id: currentUser.id, name: currentUser.dataValues.name },
      awardId: newAward?.dataValues?.id,
      note: 'Award Created',
    });

    return newAward;
  } catch (error) {
    console.error(error);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Error creating award');
  }
};

const updateOne = async (body: UpdateAward, currentUser: typeof User) => {
  const {
    id,
    programId,
    assigneeId,
    clientId,
    applicationId,
    source,
    category,
    funder,
    funderAwardId,
    startsOn,
    endsOn,
    department,
    grantPurpose,
    grantProgramName,
    notes,
    awardAmount,
    awardExpended,
    awardBalance,
    matchAmount,
    matchExpended,
    matchBalance,
    resolutionRequired,
    applicationRequired,
    awardLetterRequired,
    contractMaterialRequired,
    initialAgreementRequired,
    reportRequired,
    awardPortalLink,
  } = body;

  const fields: AwardInput = {
    id,
    programId,
    assigneeId,
    clientId,
    applicationId,
    source: Array.isArray(source) ? source[0] : source,
    category: Array.isArray(category) ? category[0] : category,
    funder: funder?.trim(),
    funderAwardId,
    startsOn,
    endsOn,
    department,
    grantPurpose,
    grantProgramName,
    notes: notes ? `${notes}\n` : undefined,
    awardAmount,
    awardExpended,
    awardBalance,
    matchAmount,
    matchExpended,
    matchBalance,
    resolutionRequired,
    applicationRequired,
    awardLetterRequired,
    contractMaterialRequired,
    initialAgreementRequired,
    reportRequired,
    awardPortalLink,
  };

  const isClientUser = [userAdmin, userAnalyst].includes(currentUser.accessLevel);
  const isDifferentClient = clientId !== currentUser.clientCreatorId;
  if (isClientUser && isDifferentClient) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'You cannot update this award.');
  }

  // Remove keys from fields wherever the value is undefined.
  Object.keys(fields).forEach(
    (key) => fields[key as keyof AwardInput] === undefined && delete fields[key as keyof AwardInput]
  );

  const fieldsWithDate = ['startsOn', 'endsOn'];

  fieldsWithDate.forEach((dateName) => {
    const dateValue = fields[dateName as keyof AwardInput];
    if (dateValue && rEDate.test(dateValue as string)) {
      [fields[dateName as keyof AwardInput] as string] = (dateValue as string).split('T');
    }
  });

  if (startsOn && endsOn && dayjs(endsOn).isBefore(startsOn)) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Start Date must be earlier than End Date.');
  }

  const award = await Award.findByPk(id);

  if (!award) throw new ApiError(httpStatus.NOT_FOUND, 'Award with the provided id not found.');
  else {
    try {
      const old = JSON.parse(JSON.stringify(award));

      await award.update(fields);

      await actionLogService.updateAwardActionLog({
        award: fields,
        user: currentUser,
        awardId: id,
        note: 'Award Updated',
        old,
      });

      return true;
    } catch (error) {
      console.error(error);

      if (error instanceof ApiError) {
        throw new ApiError(error.statusCode, error.message);
      }

      throw new ApiError(httpStatus.BAD_REQUEST, 'Error updating award.');
    }
  }
};

const deleteOne = async (awardId: number) => {
  try {
    const rowsDeleted = await Award.destroy({ where: { id: awardId } });
    if (rowsDeleted === 0) throw new ApiError(httpStatus.NOT_FOUND, 'Award not found.');
    actionLogService.deleteAwardActionLog(awardId);
    return true;
  } catch (err) {
    console.error(err);
    if (err instanceof ApiError) {
      throw new ApiError(err.statusCode, err.message);
    }
    throw new ApiError(httpStatus.BAD_REQUEST, 'Error deleting award.');
  }
};

const generateCsv = async (query: FilterAwards, currentUser: typeof User) => {
  const { headers } = strings.awards;

  // Does not work because the value is specified
  // if (currentUser.clientCreatorId) headers = headers.filter((header) => header !== 'Client');

  try {
    const results = await list({ ...query, page: '1', perPage: '10000' }, currentUser);

    const rows: (typeof Award)[] = results?.rows || [];
    const csvData = rows.map(
      ({
        client,
        funder,
        grantProgramName,
        assignee,
        department,
        status,
        startsOn,
        endsOn,
        reports,
        awardAmount,
        awardBalance,
        awardExpended,
        paymentsRequested,
        paymentsReceived,
      }) => [
        ...(client?.name ? [client.name] : ['']),
        funder,
        grantProgramName,
        assignee?.name || '',
        department,
        status,
        reduceTZ(startsOn || undefined),
        endsOn,
        reduceTZ(reports[0]?.dueDate),
        awardAmount,
        awardBalance,
        awardExpended,
        paymentsRequested,
        paymentsReceived,
      ]
    );

    const compressedFile = await createCsvFile(csvData, headers, 'awards.csv');

    return compressedFile;
  } catch (error) {
    console.error('generateCsv', error);

    throw new ApiError(httpStatus.NOT_FOUND, 'Error generating csv');
  }
};

const generateXlxs = async (query: FilterAwards, currentUser: typeof User) => {
  const { headers } = strings.awards;

  // Does not work because the value is specified
  // if (currentUser.clientCreatorId) headers = headers.filter((header) => header !== 'Client');

  try {
    const results = await list({ ...query, page: '1', perPage: '10000' }, currentUser);

    const awards: (typeof Award)[] = results.rows || [];

    const csvData = awards.map(
      ({
        client,
        funder,
        grantProgramName,
        assignee,
        department,
        status,
        startsOn,
        endsOn,
        reports,
        awardAmount,
        awardBalance,
        awardExpended,
        paymentsRequested,
        paymentsReceived,
      }) => [
        ...(client?.name ? [client.name] : ['']),
        funder,
        grantProgramName,
        assignee?.name || '',
        department,
        status,
        reduceTZ(startsOn || undefined),
        endsOn,
        reduceTZ(reports[0]?.dueDate),
        awardAmount,
        awardBalance,
        awardExpended,
        paymentsRequested,
        paymentsReceived,
      ]
    );

    const resultFile = await createXlxsFile(csvData, headers, 'Summary');

    return resultFile;
  } catch (error) {
    console.error('generateXlxs', error);

    throw new ApiError(httpStatus.BAD_REQUEST, 'Error generating xlxs');
  }
};

const getAwardsByUserId = async (userId: string, query: FilterAwards) => {
  const { page, perPage, programs, clients } = query;
  const { queryFilters, sortingQuery } = await getFilterCriteria(query);

  const intPage = parseInt(page || '1', 10);
  const intPerPage = parseInt(perPage || '20', 10);

  if (Number.isNaN(intPage) || Number.isNaN(intPerPage))
    throw new ApiError(httpStatus.BAD_REQUEST, `Invalid query parameters`);
  try {
    const awardAttributes = {
      where: {
        ...queryFilters,
        ...(userId && { assigneeId: userId }),
      },
      include: [
        {
          model: db.Employee,
          as: 'assignee',
        },
        {
          model: db.Client,
          as: 'client',
          where: clients
            ? { name: { [Op.in]: clients.split(',').map((name) => name.trim()) } }
            : {},
          required: true, // Ensures an inner join
        },
        {
          model: db.Application,
          as: 'application',
        },
        {
          model: db.Program,
          as: 'program',
          where: programs
            ? { name: { [Op.in]: programs.split(',').map((name) => name.trim()) } }
            : {},
        },
      ],
      offset: intPerPage * (intPage - 1),
      limit: intPerPage,
      ...(sortingQuery && { order: sortingQuery }),
    };

    // Performance monitoring for sorting queries
    const startTime = Date.now();
    const data = await Award.scope('defaultScope').findAndCountAll(awardAttributes);
    const queryTime = Date.now() - startTime;

    // Log slow queries for performance monitoring
    if (queryTime > 1000) {
      console.warn(`Slow award by user sorting query: ${queryTime}ms`, {
        userId,
        sortBy: query.sortBy,
        sortOrder: query.sortOrder,
        page: query.page,
        perPage: query.perPage,
        totalRecords: data.count,
        filters: Object.keys(queryFilters).length > 0 ? Object.keys(queryFilters) : 'none',
      });
    }

    return data;
  } catch (error) {
    console.error(error);
    if (error instanceof ApiError) {
      throw new ApiError(error.statusCode, error.message);
    }
    throw new ApiError(httpStatus.BAD_REQUEST, `Error retrieving awards`);
  }
};

export const createVersion = async (
  versionName: string,
  awardId: number,
  entries: any[],
  user: typeof User
) => {
  try {
    const version = await Version.create({
      versionName,
      awardId,
    });

    const budgetEntries = await Promise.all(
      entries.map((entry) => {
        entry.versionId = version.id;
        entry.userId = user.id;
        return BudgetVersionEntry.create(entry);
      })
    );

    return { version, budgetEntries };
  } catch (error) {
    console.error(error);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Error creating version.');
  }
};

export const getVersionsByAwardId = async (awardId: string, page: number, pageSize: number) => {
  try {
    const offset = (page - 1) * pageSize;
    const versions = await Version.findAll({
      where: { awardId },
      limit: pageSize,
      offset,
    });

    return versions;
  } catch (error) {
    console.error(error);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Error retrieving versions.');
  }
};

export const getVersionById = async (id: string) => {
  console.log('inside the service', id);
  try {
    const version = await Version.findByPk(id, {
      include: [
        {
          model: BudgetVersionEntry,
          as: 'budget_version_entries',
          order: [['createdAt', 'ASC']],
        },
      ],
    });

    if (!version) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Version not found.');
    }

    return version;
  } catch (error) {
    console.error(error);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Error retrieving version.');
  }
};

export default {
  list,
  getAwardsByUserId,
  search,
  getById,
  createOne,
  updateOne,
  deleteOne,

  // Budget Entries
  addBudgetEntry,
  addManyBudgetEntries,
  updateBudgetEntry,
  updateManyBudgetEntries,
  getBudgetEntries,
  deleteBudgetEntry,

  // Award Documents
  getFiles,
  createFile,
  deleteFile,
  downloadZippedFiles,

  // Award Timelines
  requestApproval,
  approveAward,
  rejectAward,
  updateAwardStatus,
  syncAwardStatus,
  getAwardTimelines,

  // Award Reports
  listReports,
  addReport,
  updateReport,
  deleteReport,

  // Data Exports
  generateCsv,
  generateXlxs,

  // Award Payments
  addPayment,
  getPaymentById,
  listPayments,
  updatePayment,
  deletePayment,

  // User Roles
  getAllUserRoles,
  getUserRoleById,
  createUserRole,
  updateUserRole,
  deleteUserRole,

  // Award version budget entries
  createVersion,
  getVersionsByAwardId,
  getVersionById,
};
