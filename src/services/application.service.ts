import dayjs from 'dayjs';
import Sequelize, { Op } from 'sequelize';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import httpStatus from 'http-status';
import { snakeCase } from 'lodash';

import { createAwardFromApplication } from '../helpers/applications/create-award.helper';
import {
  CreateApplication,
  FilterApplications,
  UpdateApplication,
} from '../controllers/types/application.type';

import { ApplicationInput } from '../models/types/application';
import { UserOuput } from '../models/types/user';
import ApiError from '../helpers/api-error';
import { getFilterCriteria } from '../helpers/application.helper';

import actionLogService from './action-log.service';
import awardService from './award.service';
import { generateCsv, generateXlxs } from './applications/application-export-service';
import { createMany, updateMany, deleteMany } from './applications/application-bulk-service';
import { sendNewAppEmails, sendMultiAppEmails } from './applications/application-email-service';

import db from '../models';

import { ClientOuput } from '../models/types/client';

require('dotenv').config();

const strings = require('../config/strings');
const programService = require('./program.service');

const { Application, ApplicationFiles } = db;
// eslint-disable-next-line no-useless-escape
const rEDate = /[\d\-]{8,10}T[\d\:\.]{8,12}Z/;

dayjs.extend(advancedFormat);

const {
  applications: { statuses },
} = strings;

const list = async (query: FilterApplications, currentUser: UserOuput) => {
  const { page, perPage, clients, target } = query;
  let clientIds: number[] = [];
  const clientIdOrNameString = clients
    ?.toString()
    .split(',')
    .map((name) => name.trim());

  if (clients) {
    const records: ClientOuput[] = await db.Client.findAll({
      where: {
        [Op.or]: {
          name: {
            [Op.in]: clientIdOrNameString,
          },
          id: {
            [Op.in]: clientIdOrNameString,
          },
        },
      },
      attributes: ['id'],
    });

    if (records.length === 0) {
      return { count: 0, rows: [] };
    }

    clientIds = (records || []).map((record) => record.id as number);
  }
  const { queryFilters, sortingQuery } = getFilterCriteria(query, clientIds);
  const dateType = query.endDateType || 'endsAt';

  const intPage = parseInt(page || '1', 10);
  const intPerPage = parseInt(perPage || '20', 10);

  if (Number.isNaN(intPage) || Number.isNaN(intPerPage))
    throw new ApiError(httpStatus.BAD_REQUEST, `Invalid query parameters`);
  try {
    if (target === 'om') {
      if (currentUser) {
        queryFilters.assignee_id = currentUser.id;
      }
    }
    const applicationAttributes = {
      attributes:
        dateType === 'submissionDate'
          ? ['id', 'funder', 'name', 'startsAt', dateType, 'status']
          : ['id', 'funder', 'name', 'startsAt', dateType, 'submissionDate', 'status'],
      where: {
        ...queryFilters,
      },
      offset: intPerPage * (intPage - 1),
      limit: perPage,
      order: Sequelize.literal(
        `${
          dateType === 'endsAt' ? sortingQuery?.replace('LAST', 'FIRST') : sortingQuery
        }, DATE(${snakeCase(dateType)}) < CURRENT_DATE NULLS FIRST, ABS(DATE(${snakeCase(
          dateType
        )}) - CURRENT_DATE) NULLS FIRST `
      ),
    };

    const data = await Application.findAndCountAll(applicationAttributes);

    return data;
  } catch (error) {
    console.error(error);
    throw new ApiError(httpStatus.BAD_REQUEST, `Error retrieving applications`);
  }
};

const search = async (field: keyof ApplicationInput, query: string, currentUser: UserOuput) => {
  try {
    // Sequelize offers NO NATIVE SOLUTION for supporting DISTINCT ON keyword of PostgreSQL. Hacks to the rescue! ¯\_(ツ)_/¯
    // See this old issue: https://github.com/sequelize/sequelize/issues/5475
    const results = await Application.findByQuery(field, query, [
      Sequelize.literal('DISTINCT ON("name") "name"'),
      'id',
      'name',
    ]);

    return results.filter((result: typeof Application) => {
      if (currentUser.clientCreatorId)
        return Number(result.clientId) === Number(currentUser.clientCreatorId);

      return true;
    });
  } catch (error) {
    console.error(error);
    throw new ApiError(httpStatus.BAD_REQUEST, `Error searcing applications`);
  }
};

const getById = async (id: string) => {
  try {
    const application = await Application.findByPk(parseInt(id, 10));
    const actionLog = await actionLogService.getByApplicationId(Number(id));

    if (!application) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Application not found');
    } else {
      // application.dataValues.endsAt = application.dataValues.endsAt ? dayjs(application.dataValues.endsAt).format("MM-DD-YYYY") : null;
      application.dataValues.actionLogs = actionLog?.dataValues || null;
      return application;
    }
  } catch (err) {
    console.error(err);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Error retieving application');
  }
};

const createOne = async (applicationBody: CreateApplication, currentUser: UserOuput) => {
  const {
    programId,
    assigneeId,
    clientId,
    name,
    funder,
    status,
    fundingAmount,
    varyingFundingAmount,
    amountVaries,
    source,
    dateAwarded,
    submissionDate,
    category,
    departmentNotified,
    matchRequirements,
    performancePeriod,
    submissionStatus,
    customFields,
    dateNotified,
    startsAt,
    endsAt,
    grantPurpose,
    estimatedResponse,
    summaryFile,
  } = applicationBody;

  // Parse category value properly
  let parsedCategory = category;
  if (typeof category === 'string' && (category as string).includes(',')) {
    parsedCategory = parseInt((category as string).split(',')[0], 10);
  } else if (typeof category === 'string') {
    parsedCategory = parseInt(category, 10);
  }

  const applicationFields: ApplicationInput = {
    programId,
    assigneeId,
    clientId,
    name,
    status: status ?? statuses.clientNotified,
    funder: funder?.trim(),
    fundingAmount,
    varyingFundingAmount,
    amountVaries,
    source,
    category: parsedCategory,
    departmentNotified,
    matchRequirements,
    performancePeriod,
    submissionStatus,
    customFields,
    startsAt: startsAt && dayjs(new Date(startsAt).toISOString()).format('YYYY-MM-DD'),
    endsAt: endsAt && dayjs(new Date(endsAt).toISOString()).format('YYYY-MM-DD'),
    grantPurpose,
    estimatedResponse,
    summaryFile,
    notifyDate: dateNotified,
    awardDate: dateAwarded,
    submissionDate,
  };

  if (rEDate.test(endsAt)) {
    applicationFields.endsAt = endsAt;
  }

  if (!status) applicationFields.status = statuses.clientNotified;
  if (status === statuses.clientNotified && !applicationFields.notifyDate)
    applicationFields.notifyDate = dayjs().format('YYYY-MM-DD');

  // Remove keys from applicationFields wherever the value is undefined.
  Object.keys(applicationFields).forEach(
    (key) =>
      applicationFields[key as keyof ApplicationInput] === undefined &&
      delete applicationFields[key as keyof ApplicationInput]
  );
  try {
    const newApplication = await Application.create(applicationFields);
    if (status === strings?.applications?.statuses?.grantAwarded) {
      const program = await programService.getById(programId);
      awardService.createOne(
        {
          applicationId: newApplication?.dataValues?.id,
          programId,
          clientId,
          category,
          source,
          funder,
          grantPurpose,
          grantProgramName: program.name,
        },
        currentUser
      );
    }

    await actionLogService.createApplicationActionLog({
      application: applicationFields,
      user: { id: currentUser.id, name: currentUser.dataValues.name },
      appId: newApplication?.dataValues?.id,
      note: 'Application Created',
    });

    return newApplication;
  } catch (error) {
    console.error(error);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Error creating application');
  }
};

const updateOne = async (applicationBody: UpdateApplication, user: UserOuput) => {
  const {
    id,
    assigneeId,
    clientId,
    status,
    name,
    funder,
    fundingAmount,
    varyingFundingAmount,
    amountVaries,
    source,
    category,
    departmentNotified,
    matchRequirements,
    performancePeriod,
    submissionStatus,
    customFields,
    dateNotified,
    submissionDate,
    dateAwarded,
    startsAt,
    endsAt,
    grantPurpose,
    estimatedResponse,
    summaryFile,
    generateAward, // pseudo field
  } = applicationBody;

  // Process the assigneeId field:
  let finalAssigneeId = assigneeId;
  if (assigneeId && typeof assigneeId === 'string' && !/^\d+$/.test(assigneeId)) {
    // Lookup user by name if the provided assignee is non-numeric
    const { Employee } = db;
    const foundUser = await Employee.findOne({
      where: { name: { [Op.iLike]: `%${assigneeId.trim()}%` } },
    });
    finalAssigneeId = foundUser ? foundUser.id : null;
  }

  const applicationFields: ApplicationInput = {
    id,
    assigneeId: finalAssigneeId,
    clientId,
    name,
    status,
    funder: funder?.trim(),
    fundingAmount,
    varyingFundingAmount,
    amountVaries,
    source,
    category,
    departmentNotified,
    matchRequirements,
    performancePeriod,
    submissionStatus,
    customFields,
    notifyDate: dateNotified,
    submissionDate,
    awardDate: dateAwarded,
    startsAt,
    endsAt: endsAt ? new Date(endsAt).toISOString() : null,
    grantPurpose,
    estimatedResponse,
    summaryFile,
  };

  if (rEDate.test(endsAt)) {
    applicationFields.endsAt = endsAt;
  }

  if (typeof category === 'string' && (category as string).includes(',')) {
    applicationFields.category = parseInt((category as string).split(',')[0], 10);
  } else {
    applicationFields.category = category;
  }

  // Remove keys with undefined values
  Object.keys(applicationFields).forEach(
    (key) =>
      applicationFields[key as keyof ApplicationInput] === undefined &&
      delete applicationFields[key as keyof ApplicationInput]
  );

  // Convert date fields if needed
  const fieldsWithDate = ['notifyDate', 'submissionDate', 'awardDate', 'startsAt'];
  fieldsWithDate.forEach((dateName) => {
    const dateValue = applicationFields[dateName as keyof ApplicationInput];
    if (dateValue && rEDate.test(dateValue as string)) {
      [applicationFields[dateName as keyof ApplicationInput] as string] = (
        dateValue as string
      ).split('T');
    }
  });

  if (startsAt && endsAt && dayjs(endsAt).isBefore(startsAt)) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Start Date must be earlier than Due Date.');
  }

  const application = await Application.findByPk(id);
  if (!application)
    throw new ApiError(httpStatus.NOT_FOUND, 'Application with the provided id not found.');
  else {
    try {
      const old = JSON.parse(JSON.stringify(application));
      await application.update(applicationFields);
      createAwardFromApplication(
        user,
        { ...application, ...applicationFields },
        old,
        generateAward
      );
      await actionLogService.updateApplicationActionLog({
        application: applicationFields,
        user,
        appId: id,
        note: 'Application Updated',
        old,
      });
      return true;
    } catch (error) {
      console.error(error);
      throw new ApiError(httpStatus.BAD_REQUEST, 'Error updating application.');
    }
  }
};

const deleteOne = async (id: number) => {
  try {
    const rowsDeleted = await Application.destroy({ where: { id } });
    if (rowsDeleted === 0) throw new ApiError(httpStatus.NOT_FOUND, 'Application not found.');

    await actionLogService.deleteApplicationActionLog(id);
    return true;
  } catch (err) {
    console.error(err);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Error deleting application.');
  }
};

const createFile = async (fileFields: any) => {
  try {
    const result = await ApplicationFiles.create(fileFields);
    return result;
  } catch (error) {
    console.error('Error uploading file:', error);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Error creating application file');
  }
};

const deleteFile = async (id: string) => {
  console.log('inside severvice', id);
  try {
    const rowsDeleted = await ApplicationFiles.destroy({ where: { id } });
    if (rowsDeleted === 0) throw new ApiError(httpStatus.NOT_FOUND, 'File not found');

    return true;
  } catch (error) {
    console.error(error);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Error deleting application file');
  }
};

const updateFileApplicationId = async (tempApplicationId: string, applicationId: string) => {
  try {
    console.log('paramsssssssssssss', tempApplicationId, applicationId);
    const [updatedRows] = await ApplicationFiles.update(
      { applicationId },
      { where: { applicationId: tempApplicationId } }
    );

    if (updatedRows === 0) {
      throw new ApiError(httpStatus.NOT_FOUND, 'No files found with the temporary application ID.');
    }

    return updatedRows;
  } catch (error) {
    console.error('Error updating file application IDs:', error);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Error updating file application IDs.');
  }
};

const getFilesByApplicationId = async (applicationId: string) => {
  try {
    const files = await ApplicationFiles.findAll({
      where: { applicationId },
    });

    return files;
  } catch (error) {
    console.error('Error retrieving files by application ID:', error);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Error retrieving files by application ID.');
  }
};
module.exports = {
  list,
  search,
  getById,

  createOne,
  updateOne,
  deleteOne,

  sendNewAppEmails,
  sendMultiAppEmails,

  createMany,
  updateMany,
  deleteMany,

  generateCsv,
  generateXlxs,

  createFile,
  deleteFile,
  updateFileApplicationId,
  getFilesByApplicationId,
};
